#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SMC Strategy for FreqTrade
Smart Money Concepts strategy implementation

This file imports and uses the main SMC strategy from the project's strategy module.
"""

import sys
import os
from pathlib import Path

# Add project root to Python path to import the main strategy
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    # Import the main SMC strategy
    from backtest.strategies.smc_strategy import SMCStrategy
    
    # The strategy is already FreqTrade-compatible, so we can use it directly
    # No need to create a wrapper class - just import and use
    
except ImportError as e:
    # Fallback implementation if main strategy cannot be imported
    print(f"Warning: Could not import main SMC strategy: {e}")
    print("Using fallback implementation...")
    
    from freqtrade.strategy.interface import IStrategy
    import pandas as pd
    import numpy as np
    from pandas import DataFrame
    
    class SMCStrategy(IStrategy):
        """
        Fallback SMC Strategy for FreqTrade
        
        This is a simplified version that works when the main SMC strategy
        cannot be imported due to missing dependencies.
        """
        
        # Strategy configuration
        minimal_roi = {
            "0": 0.03,    # 3% target profit
            "30": 0.02,   # 2% after 30 minutes
            "60": 0.01,   # 1% after 1 hour
            "120": 0      # Exit after 2 hours
        }
        
        stoploss = -0.02  # 2% stop loss
        timeframe = '1m'
        startup_candle_count: int = 200
        can_short: bool = True
        
        order_types = {
            'entry': 'market',
            'exit': 'market',
            'stoploss': 'market',
            'stoploss_on_exchange': False,
        }
        
        order_time_in_force = {
            'entry': 'GTC',
            'exit': 'GTC'
        }
        
        def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
            """Calculate basic indicators for fallback strategy"""
            # Basic EMA indicators
            dataframe['ema_20'] = dataframe['close'].ewm(span=20).mean()
            dataframe['ema_50'] = dataframe['close'].ewm(span=50).mean()
            dataframe['ema_200'] = dataframe['close'].ewm(span=200).mean()
            
            # RSI
            delta = dataframe['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            dataframe['rsi'] = 100 - (100 / (1 + rs))
            
            # ATR
            high_low = dataframe['high'] - dataframe['low']
            high_close = np.abs(dataframe['high'] - dataframe['close'].shift())
            low_close = np.abs(dataframe['low'] - dataframe['close'].shift())
            true_range = np.maximum(high_low, np.maximum(high_close, low_close))
            dataframe['atr'] = true_range.rolling(window=14).mean()
            
            return dataframe
        
        def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
            """Generate entry signals for fallback strategy"""
            # Long entry conditions
            dataframe.loc[
                (
                    (dataframe['ema_20'] > dataframe['ema_50']) &
                    (dataframe['rsi'] > 30) & (dataframe['rsi'] < 70) &
                    (dataframe['close'] > dataframe['ema_20']) &
                    (dataframe['volume'] > 0)
                ),
                ['enter_long', 'enter_tag']
            ] = (1, 'smc_long')
            
            # Short entry conditions
            dataframe.loc[
                (
                    (dataframe['ema_20'] < dataframe['ema_50']) &
                    (dataframe['rsi'] > 30) & (dataframe['rsi'] < 70) &
                    (dataframe['close'] < dataframe['ema_20']) &
                    (dataframe['volume'] > 0)
                ),
                ['enter_short', 'enter_tag']
            ] = (1, 'smc_short')
            
            return dataframe
        
        def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
            """Generate exit signals for fallback strategy"""
            # Long exit conditions
            dataframe.loc[
                (
                    (dataframe['ema_20'] < dataframe['ema_50']) |
                    (dataframe['rsi'] > 75) |
                    (dataframe['close'] < dataframe['ema_20'])
                ),
                ['exit_long', 'exit_tag']
            ] = (1, 'smc_exit_long')
            
            # Short exit conditions
            dataframe.loc[
                (
                    (dataframe['ema_20'] > dataframe['ema_50']) |
                    (dataframe['rsi'] < 25) |
                    (dataframe['close'] > dataframe['ema_20'])
                ),
                ['exit_short', 'exit_tag']
            ] = (1, 'smc_exit_short')
            
            return dataframe
        
        def version(self) -> str:
            """Return strategy version"""
            return "1.0.0-fallback"
